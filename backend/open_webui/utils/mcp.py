import asyncio
import aiohttp
import json
import logging
import base64
import subprocess
import tempfile
import os
from typing import Dict, List, Any, Optional
from open_webui.env import (
    AIOHTTP_CLIENT_TIMEOUT_TOOL_SERVER_DATA,
    AIOHTTP_CLIENT_SESSION_TOOL_SERVER_SSL,
)

log = logging.getLogger(__name__)


async def verify_mcp_server_connection(connection: Dict[str, Any]) -> Dict[str, Any]:
    """
    Verify connection to an MCP server.
    
    Args:
        connection: Dictionary containing MCP server connection details
        
    Returns:
        Dictionary with connection status and details
        
    Raises:
        Exception: If connection fails
    """
    url = connection.get("url")
    transport_type = connection.get("transport_type", "http")
    auth_type = connection.get("auth_type", "bearer")
    
    if not url:
        raise Exception("Server URL is required")
    
    headers = {
        "Accept": "application/json, text/event-stream",
        "Content-Type": "application/json",
    }
    
    # Handle authentication
    if auth_type == "bearer" and connection.get("key"):
        headers["Authorization"] = f"Bearer {connection.get('key')}"
    elif auth_type == "basic" and connection.get("username") and connection.get("password"):
        credentials = base64.b64encode(
            f"{connection.get('username')}:{connection.get('password')}".encode()
        ).decode()
        headers["Authorization"] = f"Basic {credentials}"
    
    # MCP initialization request
    init_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-06-18",
            "capabilities": {
                "roots": {"listChanged": True},
                "sampling": {}
            },
            "clientInfo": {
                "name": "open-webui",
                "version": "1.0.0"
            }
        }
    }
    
    try:
        timeout = aiohttp.ClientTimeout(total=AIOHTTP_CLIENT_TIMEOUT_TOOL_SERVER_DATA)
        async with aiohttp.ClientSession(timeout=timeout, trust_env=True) as session:
            if transport_type == "http_sse":
                # For SSE, try GET first to establish SSE connection
                async with session.get(
                    url, 
                    headers={**headers, "Accept": "text/event-stream"}, 
                    ssl=AIOHTTP_CLIENT_SESSION_TOOL_SERVER_SSL
                ) as response:
                    if response.status == 200:
                        return {
                            "status": "connected", 
                            "transport": "http_sse",
                            "server_info": {
                                "url": url,
                                "transport_type": transport_type
                            }
                        }
                    else:
                        error_text = await response.text()
                        raise Exception(f"SSE connection failed with status {response.status}: {error_text}")
            else:
                # For HTTP, send POST request
                async with session.post(
                    url, 
                    headers=headers, 
                    json=init_request,
                    ssl=AIOHTTP_CLIENT_SESSION_TOOL_SERVER_SSL
                ) as response:
                    if response.status == 200:
                        try:
                            result = await response.json()
                            return {
                                "status": "connected", 
                                "transport": "http", 
                                "result": result,
                                "server_info": {
                                    "url": url,
                                    "transport_type": transport_type,
                                    "capabilities": result.get("result", {}).get("capabilities", {})
                                }
                            }
                        except json.JSONDecodeError:
                            raise Exception("Invalid JSON response from MCP server")
                    else:
                        error_text = await response.text()
                        raise Exception(f"HTTP connection failed with status {response.status}: {error_text}")
                    
    except aiohttp.ClientError as e:
        log.exception(f"Failed to verify MCP server connection to {url}")
        raise Exception(f"Network error: {str(e)}")
    except Exception as e:
        log.exception(f"Failed to verify MCP server connection to {url}")
        raise Exception(f"Connection failed: {str(e)}")


async def get_mcp_server_capabilities(connection: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get capabilities from an MCP server after successful connection.
    
    Args:
        connection: Dictionary containing MCP server connection details
        
    Returns:
        Dictionary with server capabilities
    """
    verification_result = await verify_mcp_server_connection(connection)
    
    if verification_result.get("status") == "connected":
        return verification_result.get("server_info", {}).get("capabilities", {})
    
    return {}


async def discover_mcp_tools(connection: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Discover available tools from an MCP server.

    Args:
        connection: MCP server connection details

    Returns:
        List of tool definitions with their schemas
    """
    url = connection.get("url")
    transport_type = connection.get("transport_type", "http")
    auth_type = connection.get("auth_type", "bearer")

    headers = {
        "Accept": "application/json, text/event-stream",
        "Content-Type": "application/json",
    }

    # Handle authentication
    if auth_type == "bearer" and connection.get("key"):
        headers["Authorization"] = f"Bearer {connection.get('key')}"
    elif auth_type == "basic" and connection.get("username") and connection.get("password"):
        credentials = base64.b64encode(
            f"{connection.get('username')}:{connection.get('password')}".encode()
        ).decode()
        headers["Authorization"] = f"Basic {credentials}"

    # First initialize the session
    init_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-06-18",
            "capabilities": {
                "roots": {"listChanged": True},
                "sampling": {}
            },
            "clientInfo": {
                "name": "open-webui",
                "version": "1.0.0"
            }
        }
    }

    # Then list available tools
    list_tools_request = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/list",
        "params": {}
    }

    try:
        timeout = aiohttp.ClientTimeout(total=AIOHTTP_CLIENT_TIMEOUT_TOOL_SERVER_DATA)
        async with aiohttp.ClientSession(timeout=timeout, trust_env=True) as session:
            if transport_type == "http_sse":
                # For SSE, we would need to handle streaming differently
                # For now, return empty list for SSE connections
                log.warning(f"Tool discovery not yet implemented for SSE transport: {url}")
                return []
            else:
                # Initialize session
                async with session.post(
                    url,
                    headers=headers,
                    json=init_request,
                    ssl=AIOHTTP_CLIENT_SESSION_TOOL_SERVER_SSL
                ) as response:
                    if response.status != 200:
                        raise Exception(f"Failed to initialize MCP session: {response.status}")

                    init_result = await response.json()
                    if "error" in init_result:
                        raise Exception(f"MCP initialization error: {init_result['error']}")

                # List tools
                async with session.post(
                    url,
                    headers=headers,
                    json=list_tools_request,
                    ssl=AIOHTTP_CLIENT_SESSION_TOOL_SERVER_SSL
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "error" in result:
                            raise Exception(f"MCP tools/list error: {result['error']}")

                        tools = result.get("result", {}).get("tools", [])
                        log.info(f"Discovered {len(tools)} tools from MCP server {url}")
                        return tools
                    else:
                        error_text = await response.text()
                        raise Exception(f"Failed to list tools: {response.status} - {error_text}")

    except Exception as e:
        log.exception(f"Failed to discover tools from MCP server {url}")
        raise Exception(f"Tool discovery failed: {str(e)}")


async def get_mcp_servers_data(
    servers: List[Dict[str, Any]], session_token: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Get data from multiple MCP servers concurrently, including their available tools.

    Args:
        servers: List of MCP server connection configurations
        session_token: Optional session token for authentication

    Returns:
        List of server data dictionaries for successfully connected servers with their tools
    """
    # Prepare list of enabled servers along with their original index
    server_entries = []
    for idx, server in enumerate(servers):
        if server.get("config", {}).get("enable", True):
            server_entries.append((idx, server))

    if not server_entries:
        log.info("No enabled MCP servers found")
        return []

    # Create async tasks to verify connections and discover tools
    tasks = []
    for idx, server in server_entries:
        # Use session token if auth_type is session
        if server.get("auth_type") == "session" and session_token:
            server_copy = server.copy()
            server_copy["key"] = session_token
            server_copy["auth_type"] = "bearer"
            tasks.append(_get_server_data_with_tools(idx, server_copy))
        else:
            tasks.append(_get_server_data_with_tools(idx, server))

    # Execute tasks concurrently
    responses = await asyncio.gather(*tasks, return_exceptions=True)

    # Build final results
    results = []
    for response in responses:
        if isinstance(response, Exception):
            log.error(f"Failed to get MCP server data: {str(response)}")
            continue

        if response:
            results.append(response)

    log.info(f"Successfully connected to {len(results)} out of {len(server_entries)} enabled MCP servers")
    return results


async def _get_server_data_with_tools(idx: int, server: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Helper function to get server data including tools for a single server.
    """
    try:
        # First verify connection
        connection_result = await verify_mcp_server_connection(server)

        if connection_result.get("status") != "connected":
            return None

        # Then discover tools
        tools = []
        try:
            tools = await discover_mcp_tools(server)
        except Exception as e:
            log.warning(f"Failed to discover tools from {server.get('url')}: {str(e)}")
            # Continue without tools rather than failing completely

        return {
            "idx": idx,
            "url": server.get("url"),
            "transport_type": server.get("transport_type", "http"),
            "status": connection_result.get("status"),
            "capabilities": connection_result.get("server_info", {}).get("capabilities", {}),
            "info": server.get("info", {}),
            "config": server.get("config", {}),
            "tools": tools
        }

    except Exception as e:
        log.error(f"Failed to get data for MCP server {server.get('url')}: {str(e)}")
        return None


async def send_mcp_request(
    connection: Dict[str, Any], 
    method: str, 
    params: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Send a JSON-RPC request to an MCP server.
    
    Args:
        connection: MCP server connection details
        method: JSON-RPC method name
        params: Optional parameters for the request
        request_id: Optional request ID (auto-generated if not provided)
        
    Returns:
        JSON-RPC response from the server
        
    Raises:
        Exception: If the request fails
    """
    url = connection.get("url")
    auth_type = connection.get("auth_type", "bearer")
    
    headers = {
        "Accept": "application/json, text/event-stream",
        "Content-Type": "application/json",
    }
    
    # Handle authentication
    if auth_type == "bearer" and connection.get("key"):
        headers["Authorization"] = f"Bearer {connection.get('key')}"
    elif auth_type == "basic" and connection.get("username") and connection.get("password"):
        credentials = base64.b64encode(
            f"{connection.get('username')}:{connection.get('password')}".encode()
        ).decode()
        headers["Authorization"] = f"Basic {credentials}"
    
    # Build JSON-RPC request
    request_data = {
        "jsonrpc": "2.0",
        "id": request_id or f"req_{asyncio.current_task().get_name()}_{method}",
        "method": method
    }
    
    if params is not None:
        request_data["params"] = params
    
    try:
        timeout = aiohttp.ClientTimeout(total=AIOHTTP_CLIENT_TIMEOUT_TOOL_SERVER_DATA)
        async with aiohttp.ClientSession(timeout=timeout, trust_env=True) as session:
            async with session.post(
                url,
                headers=headers,
                json=request_data,
                ssl=AIOHTTP_CLIENT_SESSION_TOOL_SERVER_SSL
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if "error" in result:
                        raise Exception(f"MCP server error: {result['error']}")
                    return result
                else:
                    error_text = await response.text()
                    raise Exception(f"HTTP error {response.status}: {error_text}")
                    
    except Exception as e:
        log.exception(f"Failed to send MCP request to {url}")
        raise Exception(f"Request failed: {str(e)}")
