<script lang="ts">
	import { getContext } from 'svelte';
	import { toast } from 'svelte-sonner';

	import Modal from '$lib/components/common/Modal.svelte';
	import SensitiveInput from '$lib/components/common/SensitiveInput.svelte';
	import Switch from '$lib/components/common/Switch.svelte';
	import { verifyMCPServerConnection } from '$lib/apis/configs';
	import AccessControl from './workspace/common/AccessControl.svelte';

	export let onSubmit: Function = () => {};
	export let onDelete: Function = () => {};
	export let show = false;
	export let edit = false;
	export let connection = null;
	export let direct = false;

	const i18n = getContext('i18n');

	let loading = false;
	let url = '';
	let transport_type = 'http';
	let auth_type = 'bearer';
	let key = '';
	let username = '';
	let password = '';
	let name = '';
	let description = '';
	let enable = true;
	let accessControl = null;

	$: if (connection) {
		url = connection.url ?? '';
		transport_type = connection.transport_type ?? 'http';
		auth_type = connection.auth_type ?? 'bearer';
		key = connection.key ?? '';
		username = connection.username ?? '';
		password = connection.password ?? '';
		name = connection.info?.name ?? '';
		description = connection.info?.description ?? '';
		enable = connection.config?.enable ?? true;
		accessControl = connection.config?.access_control ?? null;
	}

	const testConnectionHandler = async () => {
		loading = true;

		const connectionData = {
			url,
			transport_type,
			auth_type,
			key: auth_type === 'bearer' ? key : undefined,
			username: auth_type === 'basic' ? username : undefined,
			password: auth_type === 'basic' ? password : undefined,
			config: {
				enable: enable,
				access_control: accessControl
			},
			info: {
				name,
				description
			}
		};

		const res = await verifyMCPServerConnection(localStorage.token, connectionData).catch((err) => {
			toast.error($i18n.t('Connection failed'));
			loading = false;
		});

		if (res) {
			toast.success($i18n.t('Connection successful'));
			console.debug('MCP Connection successful', res);
		}
		loading = false;
	};

	const submitHandler = async () => {
		loading = true;

		url = url.replace(/\/$/, '');

		const mcpConnection = {
			url,
			transport_type,
			auth_type,
			key: auth_type === 'bearer' ? key : undefined,
			username: auth_type === 'basic' ? username : undefined,
			password: auth_type === 'basic' ? password : undefined,
			config: {
				enable: enable,
				access_control: accessControl
			},
			info: {
				name: name,
				description: description
			}
		};

		await onSubmit(mcpConnection);

		loading = false;
		show = false;

		// Reset form
		url = '';
		transport_type = 'http';
		auth_type = 'bearer';
		key = '';
		username = '';
		password = '';
		name = '';
		description = '';
		enable = true;
		accessControl = null;
	};
</script>

<Modal size="sm" bind:show>
	<div>
		<div class=" flex justify-between dark:text-gray-100 px-5 pt-4 pb-2">
			<div class=" text-lg font-medium self-center font-primary">
				{#if edit}
					{$i18n.t('Edit MCP Server')}
				{:else}
					{$i18n.t('Add MCP Server')}
				{/if}
			</div>
			<button
				class="self-center"
				on:click={() => {
					show = false;
				}}
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					viewBox="0 0 20 20"
					fill="currentColor"
					class="w-5 h-5"
				>
					<path
						d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"
					/>
				</svg>
			</button>
		</div>

		<form
			class="flex flex-col max-w-2xl mx-auto mt-4 mb-3"
			on:submit|preventDefault={() => {
				submitHandler();
			}}
		>
			<div class="flex flex-col gap-3 px-5 pb-4">
				<div class=" flex flex-col gap-1">
					<div class="flex flex-col w-full">
						<div class=" mb-1 text-xs text-gray-500">{$i18n.t('Name')}</div>

						<div class="flex-1">
							<input
								class="w-full rounded-lg py-2 px-4 text-sm dark:text-gray-300 dark:bg-gray-850 outline-none"
								placeholder={$i18n.t('MCP Server Name')}
								bind:value={name}
								required
							/>
						</div>
					</div>
				</div>

				<div class=" flex flex-col gap-1">
					<div class="flex flex-col w-full">
						<div class=" mb-1 text-xs text-gray-500">{$i18n.t('Description')}</div>

						<div class="flex-1">
							<input
								class="w-full rounded-lg py-2 px-4 text-sm dark:text-gray-300 dark:bg-gray-850 outline-none"
								placeholder={$i18n.t('MCP Server Description')}
								bind:value={description}
							/>
						</div>
					</div>
				</div>

				<div class=" flex flex-col gap-1">
					<div class="flex flex-col w-full">
						<div class=" mb-1 text-xs text-gray-500">{$i18n.t('Server URL')}</div>

						<div class="flex-1">
							<input
								class="w-full rounded-lg py-2 px-4 text-sm dark:text-gray-300 dark:bg-gray-850 outline-none"
								placeholder={$i18n.t('Enter MCP server URL')}
								bind:value={url}
								required
							/>
						</div>
					</div>
				</div>

				<div class=" flex flex-col gap-1">
					<div class="flex flex-col w-full">
						<div class=" mb-1 text-xs text-gray-500">{$i18n.t('Transport Type')}</div>

						<div class="flex-1">
							<select
								class="w-full rounded-lg py-2 px-4 text-sm dark:text-gray-300 dark:bg-gray-850 outline-none"
								bind:value={transport_type}
								required
							>
								<option value="http">{$i18n.t('HTTP')}</option>
								<option value="http_sse">{$i18n.t('HTTP with Server-Sent Events')}</option>
							</select>
						</div>
					</div>
				</div>

				<div class=" flex flex-col gap-1">
					<div class="flex flex-col w-full">
						<div class=" mb-1 text-xs text-gray-500">{$i18n.t('Authentication')}</div>

						<div class="flex-1">
							<select
								class="w-full rounded-lg py-2 px-4 text-sm dark:text-gray-300 dark:bg-gray-850 outline-none"
								bind:value={auth_type}
								required
							>
								<option value="none">{$i18n.t('None')}</option>
								<option value="bearer">{$i18n.t('Bearer Token')}</option>
								<option value="basic">{$i18n.t('Basic Authentication')}</option>
							</select>
						</div>
					</div>
				</div>

				{#if auth_type === 'bearer'}
					<div class=" flex flex-col gap-1">
						<div class="flex flex-col w-full">
							<div class=" mb-1 text-xs text-gray-500">{$i18n.t('API Key')}</div>

							<div class="flex-1">
								<SensitiveInput
									placeholder={$i18n.t('Enter API Key')}
									bind:value={key}
								/>
							</div>
						</div>
					</div>
				{:else if auth_type === 'basic'}
					<div class=" flex flex-col gap-1">
						<div class="flex flex-col w-full">
							<div class=" mb-1 text-xs text-gray-500">{$i18n.t('Username')}</div>

							<div class="flex-1">
								<input
									class="w-full rounded-lg py-2 px-4 text-sm dark:text-gray-300 dark:bg-gray-850 outline-none"
									placeholder={$i18n.t('Enter Username')}
									bind:value={username}
									required
								/>
							</div>
						</div>
					</div>

					<div class=" flex flex-col gap-1">
						<div class="flex flex-col w-full">
							<div class=" mb-1 text-xs text-gray-500">{$i18n.t('Password')}</div>

							<div class="flex-1">
								<SensitiveInput
									placeholder={$i18n.t('Enter Password')}
									bind:value={password}
									required
								/>
							</div>
						</div>
					</div>
				{/if}

				<div class="flex flex-col gap-1">
					<div class="flex w-full justify-between">
						<div class=" self-center text-xs font-medium">{$i18n.t('Enable')}</div>

						<Switch bind:state={enable} />
					</div>
				</div>

				{#if !direct}
					<AccessControl bind:accessControl />
				{/if}

				<div class="flex justify-between pt-3">
					<div class="flex gap-1">
						<button
							class="px-4 py-2 bg-emerald-700 hover:bg-emerald-800 text-gray-100 transition rounded-lg"
							type="button"
							on:click={() => {
								testConnectionHandler();
							}}
							disabled={loading}
						>
							{#if loading}
								<div class="animate-spin inline-block w-4 h-4 border-[3px] border-current border-t-transparent text-white rounded-full" role="status" aria-label="loading">
									<span class="sr-only">Loading...</span>
								</div>
							{:else}
								{$i18n.t('Test Connection')}
							{/if}
						</button>

						{#if edit}
							<button
								class="px-4 py-2 bg-red-700 hover:bg-red-800 text-gray-100 transition rounded-lg"
								type="button"
								on:click={() => {
									onDelete();
								}}
							>
								{$i18n.t('Delete')}
							</button>
						{/if}
					</div>

					<div class="flex gap-1">
						<button
							class="px-4 py-2 bg-gray-700 hover:bg-gray-800 text-gray-100 transition rounded-lg"
							type="button"
							on:click={() => {
								show = false;
							}}
						>
							{$i18n.t('Cancel')}
						</button>

						<button
							class="px-4 py-2 bg-gray-900 hover:bg-gray-850 text-gray-100 transition rounded-lg"
							type="submit"
							disabled={loading}
						>
							{#if edit}
								{$i18n.t('Update')}
							{:else}
								{$i18n.t('Save')}
							{/if}
						</button>
					</div>
				</div>
			</div>
		</form>
	</div>
</Modal>
