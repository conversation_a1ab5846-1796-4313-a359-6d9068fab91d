<script lang="ts">
	import { toast } from 'svelte-sonner';
	import { createEventDispatcher, onMount, getContext, tick } from 'svelte';
	import { getModels as _getModels } from '$lib/apis';

	const dispatch = createEventDispatcher();
	const i18n = getContext('i18n');

	import { models, settings, user } from '$lib/stores';

	import Switch from '$lib/components/common/Switch.svelte';
	import Spinner from '$lib/components/common/Spinner.svelte';
	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import Plus from '$lib/components/icons/Plus.svelte';
	import Connection from '$lib/components/chat/Settings/Tools/Connection.svelte';

	import AddServerModal from '$lib/components/AddServerModal.svelte';
	import AddMCPServerModal from '$lib/components/AddMCPServerModal.svelte';
	import { getToolServerConnections, setToolServerConnections, getMCPServerConnections, setMCPServerConnections } from '$lib/apis/configs';

	export let saveSettings: Function;

	let servers = null;
	let mcpServers = null;
	let showConnectionModal = false;
	let showMCPConnectionModal = false;

	const addConnectionHandler = async (server) => {
		servers = [...servers, server];
		await updateHandler();
	};

	const addMCPConnectionHandler = async (server) => {
		mcpServers = [...mcpServers, server];
		await updateMCPHandler();
	};

	const updateHandler = async () => {
		const res = await setToolServerConnections(localStorage.token, {
			TOOL_SERVER_CONNECTIONS: servers
		}).catch((err) => {
			toast.error($i18n.t('Failed to save connections'));

			return null;
		});

		if (res) {
			toast.success($i18n.t('Connections saved successfully'));
		}
	};

	const updateMCPHandler = async () => {
		const res = await setMCPServerConnections(localStorage.token, {
			MCP_SERVER_CONNECTIONS: mcpServers
		}).catch((err) => {
			toast.error($i18n.t('Failed to save MCP connections'));

			return null;
		});

		if (res) {
			toast.success($i18n.t('MCP connections saved successfully'));
		}
	};

	onMount(async () => {
		const res = await getToolServerConnections(localStorage.token);
		servers = res.TOOL_SERVER_CONNECTIONS;

		const mcpRes = await getMCPServerConnections(localStorage.token);
		mcpServers = mcpRes.MCP_SERVER_CONNECTIONS;
	});
</script>

<AddServerModal bind:show={showConnectionModal} onSubmit={addConnectionHandler} />
<AddMCPServerModal bind:show={showMCPConnectionModal} onSubmit={addMCPConnectionHandler} />

<form
	class="flex flex-col h-full justify-between text-sm"
	on:submit|preventDefault={() => {
		updateHandler();
	}}
>
	<div class=" overflow-y-scroll scrollbar-hidden h-full">
		{#if servers !== null && mcpServers !== null}
			<div class="">
				<div class="mb-3">
					<div class=" mb-2.5 text-base font-medium">{$i18n.t('OpenAPI Tool Servers')}</div>

					<hr class=" border-gray-100 dark:border-gray-850 my-2" />

					<div class="mb-2.5 flex flex-col w-full justify-between">
						<!-- {$i18n.t(`Failed to connect to {{URL}} OpenAPI tool server`, {
							URL: 'server?.url'
						})} -->
						<div class="flex justify-between items-center mb-0.5">
							<div class="font-medium">{$i18n.t('Manage Tool Servers')}</div>

							<Tooltip content={$i18n.t(`Add Connection`)}>
								<button
									class="px-1"
									on:click={() => {
										showConnectionModal = true;
									}}
									type="button"
								>
									<Plus />
								</button>
							</Tooltip>
						</div>

						<div class="flex flex-col gap-1.5">
							{#each servers as server, idx}
								<Connection
									bind:connection={server}
									onSubmit={() => {
										updateHandler();
									}}
									onDelete={() => {
										servers = servers.filter((_, i) => i !== idx);
										updateHandler();
									}}
								/>
							{/each}
						</div>

						<div class="my-1.5">
							<div class="text-xs text-gray-500">
								{$i18n.t('Connect to your own OpenAPI compatible external tool servers.')}
							</div>
						</div>
					</div>
				</div>

				<!-- MCP Servers Section -->
				<div class="mb-3">
					<div class=" mb-2.5 text-base font-medium">{$i18n.t('MCP Servers')}</div>

					<hr class=" border-gray-100 dark:border-gray-850 my-2" />

					<div class="mb-2.5 flex flex-col w-full justify-between">
						<div class="flex justify-between items-center mb-0.5">
							<div class="font-medium">{$i18n.t('Manage MCP Servers')}</div>

							<Tooltip content={$i18n.t(`Add MCP Server`)}>
								<button
									class="px-1"
									on:click={() => {
										showMCPConnectionModal = true;
									}}
									type="button"
								>
									<Plus />
								</button>
							</Tooltip>
						</div>

						<div class="flex flex-col gap-1.5">
							{#each mcpServers as server, idx}
								<Connection
									bind:connection={server}
									onSubmit={() => {
										updateMCPHandler();
									}}
									onDelete={() => {
										mcpServers = mcpServers.filter((_, i) => i !== idx);
										updateMCPHandler();
									}}
								/>
							{/each}
						</div>

						<div class="my-1.5">
							<div class="text-xs text-gray-500">
								{$i18n.t('Connect to MCP (Model Context Protocol) servers to extend Open WebUI with additional tools and capabilities.')}
							</div>
						</div>
					</div>
				</div>
			</div>
		{:else}
			<div class="flex h-full justify-center">
				<div class="my-auto">
					<Spinner className="size-6" />
				</div>
			</div>
		{/if}
	</div>

	<div class="flex justify-end pt-3 text-sm font-medium">
		<button
			class="px-3.5 py-1.5 text-sm font-medium bg-black hover:bg-gray-900 text-white dark:bg-white dark:text-black dark:hover:bg-gray-100 transition rounded-full"
			type="submit"
		>
			{$i18n.t('Save')}
		</button>
	</div>
</form>
