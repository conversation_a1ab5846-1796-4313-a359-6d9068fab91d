<script lang="ts">
	import { getContext } from 'svelte';
	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import Cog6 from '$lib/components/icons/Cog6.svelte';
	import AddMCPServerModal from '$lib/components/AddMCPServerModal.svelte';
	import ConfirmDialog from '$lib/components/common/ConfirmDialog.svelte';

	export let connection;
	export let onSubmit = () => {};
	export let onDelete = () => {};

	const i18n = getContext('i18n');

	let showConfigModal = false;
	let showDeleteConfirmDialog = false;
</script>

<AddMCPServerModal
	edit
	bind:show={showConfigModal}
	{connection}
	onDelete={() => {
		showDeleteConfirmDialog = true;
	}}
	onSubmit={(c) => {
		connection = c;
		onSubmit(c);
	}}
/>

<ConfirmDialog
	bind:show={showDeleteConfirmDialog}
	on:confirm={() => {
		onDelete();
		showConfigModal = false;
	}}
/>

<div class="flex w-full gap-2 items-center">
	<Tooltip
		className="w-full relative"
		content={$i18n.t(`WebUI will connect to MCP server at "{{url}}"`, {
			url: connection?.url
		})}
		placement="top-start"
	>
		{#if !(connection?.config?.enable ?? true)}
			<div
				class="absolute top-0 bottom-0 left-0 right-0 opacity-60 bg-white dark:bg-gray-900 z-10"
			></div>
		{/if}
		<div class="flex w-full gap-2">
			<div class="flex-1 relative">
				<input
					class=" outline-hidden w-full bg-transparent"
					placeholder={$i18n.t('MCP Server URL')}
					value={connection?.url}
					disabled
				/>
				<div class="absolute top-2 right-2">
					<div class="text-xs px-1 bg-gray-500 text-white rounded uppercase">
						{connection?.transport_type || 'HTTP'}
					</div>
				</div>
			</div>

			<div class="flex-1">
				<input
					class=" outline-hidden bg-transparent w-full"
					placeholder={$i18n.t('Server Name')}
					value={connection?.info?.name || 'MCP Server'}
					disabled
				/>
			</div>
		</div>
	</Tooltip>

	<div class="flex gap-1">
		<Tooltip content={$i18n.t('Configure')} className="self-start">
			<button
				class="self-center p-1 bg-transparent hover:bg-gray-100 dark:bg-gray-900 dark:hover:bg-gray-850 rounded-lg transition"
				on:click={() => {
					showConfigModal = true;
				}}
				type="button"
			>
				<Cog6 />
			</button>
		</Tooltip>
	</div>
</div>
