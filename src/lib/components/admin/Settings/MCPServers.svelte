<script lang="ts">
	import { getContext, onMount } from 'svelte';
	import { toast } from 'svelte-sonner';

	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import Plus from '$lib/components/icons/Plus.svelte';
	import Connection from './MCPServers/Connection.svelte';

	import AddMCPServerModal from '$lib/components/AddMCPServerModal.svelte';
	import { getMCPServerConnections, setMCPServerConnections } from '$lib/apis/configs';

	export let saveSettings: Function;

	const i18n = getContext('i18n');

	let servers = null;
	let showConnectionModal = false;

	const addConnectionHandler = async (server) => {
		servers = [...servers, server];
		await updateHandler();
	};

	const updateHandler = async () => {
		const res = await setMCPServerConnections(localStorage.token, {
			MCP_SERVER_CONNECTIONS: servers
		}).catch((err) => {
			toast.error($i18n.t('Failed to save MCP connections'));
			return null;
		});

		if (res) {
			toast.success($i18n.t('MCP connections saved successfully'));
		}
	};

	onMount(async () => {
		const res = await getMCPServerConnections(localStorage.token);
		servers = res.MCP_SERVER_CONNECTIONS;
	});
</script>

<AddMCPServerModal bind:show={showConnectionModal} onSubmit={addConnectionHandler} />

<form
	class="flex flex-col h-full justify-between text-sm"
	on:submit|preventDefault={() => {
		updateHandler();
	}}
>
	<div class=" overflow-y-scroll scrollbar-hidden h-full">
		{#if servers !== null}
			<div class="">
				<div class="mb-3">
					<div class=" mb-2.5 text-base font-medium">{$i18n.t('MCP Servers')}</div>

					<hr class=" border-gray-100 dark:border-gray-850 my-2" />

					<div class="mb-2.5 flex flex-col w-full justify-between">
						<div class="flex justify-between items-center mb-0.5">
							<div class="font-medium">{$i18n.t('Manage MCP Servers')}</div>

							<Tooltip content={$i18n.t(`Add MCP Server`)}>
								<button
									class="px-1"
									on:click={() => {
										showConnectionModal = true;
									}}
									type="button"
								>
									<Plus />
								</button>
							</Tooltip>
						</div>

						<div class="flex flex-col gap-1.5">
							{#each servers as server, idx}
								<Connection
									bind:connection={server}
									onSubmit={() => {
										updateHandler();
									}}
									onDelete={() => {
										servers = servers.filter((_, i) => i !== idx);
										updateHandler();
									}}
								/>
							{/each}
						</div>

						<div class="my-1.5">
							<div class="text-xs text-gray-500">
								{$i18n.t('Connect to MCP (Model Context Protocol) servers to extend Open WebUI with additional tools and capabilities.')}
								<br />
								{$i18n.t('MCP servers provide standardized access to external resources, tools, and data sources.')}
							</div>
						</div>
					</div>
				</div>
			</div>
		{/if}
	</div>

	<div class="flex justify-end pt-3 text-sm font-medium">
		<button
			class=" px-4 py-2 bg-emerald-700 hover:bg-emerald-800 text-gray-100 transition rounded-lg"
			type="submit"
		>
			{$i18n.t('Save')}
		</button>
	</div>
</form>
